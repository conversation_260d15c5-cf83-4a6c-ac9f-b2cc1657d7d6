import request from '@/utils/request'

export interface Sighting {
  id: number
  post_id: number
  sighting_location: string
  sighting_time: string
  sighting_photo_url?: string
  description?: string
  reporter_ip?: string
  is_verified: boolean
  created_at: string
  post_info: {
    title: string
    pet_name: string
    pet_species: string
    pet_color: string
    owner_username: string
  }
}

export interface SightingListParams {
  page?: number
  limit?: number
  is_verified?: boolean
  post_id?: number
}

export interface SightingListResponse {
  data: Sighting[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface SightingStats {
  total_sightings: number
  verified_sightings: number
  unverified_sightings: number
  today_sightings: number
  week_sightings: number
  sightings_with_photo: number
}

// 获取所有线索列表
export function getAllSightings(params: SightingListParams = {}) {
  return request.get<any, SightingListResponse>('/sightings/all', { params })
}

// 删除线索
export function deleteSighting(id: number) {
  return request.delete(`/sightings/${id}`)
}

// 获取线索统计
export function getSightingStats() {
  return request.get<any, { data: SightingStats }>('/sightings/stats')
}
