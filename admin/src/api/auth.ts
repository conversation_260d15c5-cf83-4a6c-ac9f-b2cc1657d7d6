import request from '@/utils/request'

export interface LoginParams {
  username: string
  password: string
}

export interface AdminInfo {
  id: number
  username: string
  role: string
  created_at: string
}

export interface LoginResponse {
  admin: AdminInfo
  token: string
}

// 管理员登录
export function login(data: LoginParams) {
  return request.post<any, LoginResponse>('/auth/admin/login', data)
}

// 获取当前管理员信息
export function getCurrentAdmin() {
  return request.get<any, { data: AdminInfo }>('/auth/admin/me')
}

// 修改密码
export function changePassword(data: { currentPassword: string; newPassword: string }) {
  return request.put('/auth/admin/change-password', data)
}

// 登出
export function logout() {
  return request.post('/auth/admin/logout')
}
